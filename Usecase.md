We have a Mongo DB Database with over 100 collections.
Each collection have a few Date and time fields (ISO format)

We are trying to read the records but getting DateOverFlowError in Pymongo.
Since the year is either 0000 or greater than 9999 which is not supported by Python datetime.

We need to run a script and identify the records which have the date in the above format.
either the year is 0000 or greater than 9999.

It should Give us the count and sample records for each collection.
it should check each column and if the column is a date field then it should check for the above condition.

One Approach that i am thinking is:

Just use try catch by fetching 1000 record and running normal parsing and then if we get OverFlowError then use DateTimeMS from bson for parsing the correct datetime.

save the Id of the records along with column name and value.

https://www.mongodb.com/docs/languages/python/pymongo-driver/current/data-formats/dates-and-times/#handling-out-of-range-datetimes

Finally create a csv with below format.

Database Name, Collection Name, RecordId, Field Name, Invalid Date Value
