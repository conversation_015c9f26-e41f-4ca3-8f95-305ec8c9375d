#!/usr/bin/env python3
"""
Configuration example for MongoDB Date Validator

This file shows how to programmatically configure and run the validator
instead of using command line arguments.
"""

import asyncio
from mongo_date_validator import MongoDateValidator, ValidationConfig


async def example_basic_validation():
    """Basic validation example."""
    config = ValidationConfig(
        connection_string="mongodb://localhost:27017",
        batch_size=1000,
        max_concurrent_collections=10,
        output_file="basic_validation_report.csv",
        log_level="INFO"
    )
    
    validator = MongoDateValidator(config)
    summary = await validator.validate_all_databases()
    
    # Save results
    validator.save_results_to_csv()
    validator.print_summary(summary)
    
    return summary


async def example_targeted_validation():
    """Example for scanning specific databases and excluding collections."""
    config = ValidationConfig(
        connection_string="*******************************************",
        batch_size=2000,
        max_concurrent_collections=15,
        output_file="targeted_validation_report.csv",
        log_level="DEBUG",
        databases_to_scan=["production_db", "analytics_db"],
        collections_to_exclude=["system.users", "system.roles", "temp_collections"],
        sample_limit=20
    )
    
    validator = MongoDateValidator(config)
    summary = await validator.validate_all_databases()
    
    # Save both CSV and JSON reports
    csv_file = validator.save_results_to_csv()
    json_file = validator.save_summary_report(summary)
    
    print(f"CSV Report: {csv_file}")
    print(f"JSON Summary: {json_file}")
    
    validator.print_summary(summary)
    
    return summary


async def example_high_performance_validation():
    """Example for high-performance validation of large databases."""
    config = ValidationConfig(
        connection_string="mongodb://localhost:27017",
        batch_size=5000,  # Larger batches for better performance
        max_concurrent_collections=25,  # More concurrent processing
        output_file="high_perf_validation_report.csv",
        log_level="WARNING",  # Reduce logging overhead
        sample_limit=5  # Fewer samples to reduce memory usage
    )
    
    validator = MongoDateValidator(config)
    summary = await validator.validate_all_databases()
    
    validator.save_results_to_csv()
    
    # Only print summary if issues found
    if summary['total_invalid_records_found'] > 0:
        validator.print_summary(summary)
        print(f"Found {summary['total_invalid_records_found']} invalid records!")
    else:
        print("No invalid date records found.")
    
    return summary


if __name__ == "__main__":
    # Run one of the examples
    print("Running basic validation example...")
    asyncio.run(example_basic_validation())
    
    # Uncomment to run other examples:
    # asyncio.run(example_targeted_validation())
    # asyncio.run(example_high_performance_validation())
