#!/usr/bin/env python3
"""
Test script for MongoDB Date Validator

This script creates test data with invalid dates and runs the validator to verify functionality.
"""

import asyncio
import sys
import json
from datetime import datetime
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent))

try:
    from mongo_date_validator import MongoDateValidator, ValidationConfig
    from pymongo import MongoClient
    from bson.datetime_ms import DatetimeMS
    import motor.motor_asyncio
except ImportError as e:
    print(f"Import error: {e}")
    print("Please install required dependencies: pip install -r requirements.txt")
    sys.exit(1)


async def create_test_data(connection_string: str):
    """Create test data with invalid dates for validation testing."""
    print("Creating test data with invalid dates...")
    
    try:
        client = motor.motor_asyncio.AsyncIOMotorClient(connection_string)
        db = client['test_date_validation']
        
        # Clear existing test data
        await db.drop_collection('test_collection_1')
        await db.drop_collection('test_collection_2')
        
        # Collection 1: Various invalid date scenarios
        collection1 = db['test_collection_1']
        
        test_docs_1 = [
            {
                '_id': 'doc1',
                'name': 'Test Document 1',
                'created_date': DatetimeMS.from_datetime(datetime(1, 1, 1)),  # Year 0001 (invalid)
                'updated_date': datetime(2023, 5, 15),  # Valid date
                'metadata': {
                    'processed_at': DatetimeMS.from_datetime(datetime(9999, 12, 31))  # Year 9999 (valid)
                }
            },
            {
                '_id': 'doc2',
                'name': 'Test Document 2',
                'created_date': datetime(2023, 1, 1),  # Valid date
                'expiry_date': DatetimeMS.from_datetime(datetime(10000, 1, 1)),  # Year 10000 (invalid)
                'last_login': datetime(2023, 6, 1)  # Valid date
            },
            {
                '_id': 'doc3',
                'name': 'Test Document 3',
                'birth_date': DatetimeMS.from_datetime(datetime(1, 1, 1)),  # Year 0001 (invalid)
                'registration_time': datetime(2023, 3, 10),  # Valid date
                'settings': {
                    'theme_changed_at': DatetimeMS.from_datetime(datetime(15000, 6, 15))  # Year 15000 (invalid)
                }
            }
        ]
        
        await collection1.insert_many(test_docs_1)
        
        # Collection 2: More test scenarios
        collection2 = db['test_collection_2']
        
        test_docs_2 = [
            {
                '_id': 'doc4',
                'title': 'Another Test Document',
                'publish_date': datetime(2023, 7, 1),  # Valid date
                'archive_date': DatetimeMS.from_datetime(datetime(50000, 1, 1)),  # Year 50000 (invalid)
            },
            {
                '_id': 'doc5',
                'title': 'Valid Document',
                'created_at': datetime(2023, 8, 1),  # Valid date
                'modified_at': datetime(2023, 8, 15),  # Valid date
            }
        ]
        
        await collection2.insert_many(test_docs_2)
        
        print("Test data created successfully!")
        print("- test_collection_1: 3 documents (4 invalid dates)")
        print("- test_collection_2: 2 documents (1 invalid date)")
        print("Total expected invalid dates: 5")
        
        client.close()
        
    except Exception as e:
        print(f"Error creating test data: {e}")
        raise


async def run_validation_test(connection_string: str):
    """Run the validation test on the created test data."""
    print("\nRunning validation test...")

    # Create a test config file
    test_config = {
        "connection_string": connection_string,
        "databases": "test_date_validation",
        "exclude_collections": None,
        "batch_size": 100,
        "max_concurrent_collections": 5,
        "output_file": "test_validation_report.csv",
        "sample_limit": 10,
        "log_level": "INFO",
        "no_summary": False,
        "save_summary": False
    }

    # Save test config
    with open("test_config.json", "w") as f:
        json.dump(test_config, f, indent=2)

    config = ValidationConfig(
        connection_string=connection_string,
        batch_size=100,
        max_concurrent_collections=5,
        output_file="test_validation_report.csv",
        log_level="INFO",
        databases_to_scan=["test_date_validation"],
        sample_limit=10
    )
    
    validator = MongoDateValidator(config)
    
    try:
        summary = await validator.validate_all_databases()
        
        # Save results
        csv_file = validator.save_results_to_csv()
        
        # Print results
        validator.print_summary(summary)
        
        # Verify results
        expected_invalid_count = 5
        actual_invalid_count = summary['total_invalid_records_found']
        
        print(f"\nTest Results:")
        print(f"Expected invalid records: {expected_invalid_count}")
        print(f"Found invalid records: {actual_invalid_count}")
        
        if actual_invalid_count == expected_invalid_count:
            print("✅ TEST PASSED: Found expected number of invalid records")
        else:
            print("❌ TEST FAILED: Invalid record count mismatch")
            
        print(f"Detailed results saved to: {csv_file}")
        
        return actual_invalid_count == expected_invalid_count
        
    except Exception as e:
        print(f"Error during validation: {e}")
        return False


async def cleanup_test_data(connection_string: str):
    """Clean up test data after testing."""
    print("\nCleaning up test data...")
    
    try:
        client = motor.motor_asyncio.AsyncIOMotorClient(connection_string)
        db = client['test_date_validation']
        
        await db.drop_collection('test_collection_1')
        await db.drop_collection('test_collection_2')
        
        client.close()
        print("Test data cleaned up successfully!")
        
    except Exception as e:
        print(f"Error cleaning up test data: {e}")


async def main():
    """Main test function."""
    # Default connection string for local MongoDB
    connection_string = "mongodb://localhost:27017"
    
    if len(sys.argv) > 1:
        connection_string = sys.argv[1]
    
    print("MongoDB Date Validator Test")
    print("=" * 50)
    print(f"Connection: {connection_string}")
    print()
    
    try:
        # Create test data
        await create_test_data(connection_string)
        
        # Run validation
        test_passed = await run_validation_test(connection_string)
        
        # Clean up
        await cleanup_test_data(connection_string)
        
        # Final result
        if test_passed:
            print("\n🎉 ALL TESTS PASSED!")
            sys.exit(0)
        else:
            print("\n💥 TESTS FAILED!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\nTest execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
