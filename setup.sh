#!/bin/bash

# MongoDB Date Validator Setup Script
# This script sets up the environment and dependencies for the MongoDB Date Validator

echo "MongoDB Date Validator Setup"
echo "============================"

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.7 or higher."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3."
    exit 1
fi

echo "✅ pip3 found: $(pip3 --version)"

# Install dependencies
echo ""
echo "Installing dependencies..."
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully!"
else
    echo "❌ Failed to install dependencies. Please check the error messages above."
    exit 1
fi

# Make scripts executable
chmod +x mongo_date_validator.py
chmod +x test_validator.py
chmod +x config_example.py

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Usage Examples:"
echo "==============="
echo ""
echo "1. Basic validation (using config.json):"
echo "   python3 mongo_date_validator.py"
echo ""
echo "2. Use custom config file:"
echo "   python3 mongo_date_validator.py --config production_config.json"
echo ""
echo "3. Mix config file with command line overrides:"
echo "   python3 mongo_date_validator.py --databases \"db1,db2\" --batch-size 2000"
echo ""
echo "4. Pure command line approach:"
echo "   python3 mongo_date_validator.py --connection-string \"mongodb://localhost:27017\""
echo ""
echo "5. Run tests (requires local MongoDB):"
echo "   python3 test_validator.py"
echo ""
echo "6. View help:"
echo "   python3 mongo_date_validator.py --help"
echo ""
echo "Configuration:"
echo "=============="
echo "Edit config.json to set your MongoDB connection and preferences."
echo "Sample configurations are available in config_samples/ directory."
echo ""
echo "For more information, see README.md"
