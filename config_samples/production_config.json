{"_comment": "Production environment configuration for MongoDB Date Validator", "connection_string": "******************************************************************************************", "databases": "production_db,analytics_db,user_data_db", "exclude_collections": "system.users,system.roles,system.indexes,temp_collections,cache_data", "batch_size": 2000, "max_concurrent_collections": 15, "output_file": "production_invalid_dates_report.csv", "sample_limit": 20, "log_level": "WARNING", "no_summary": false, "save_summary": true}