{"_comment": "Development environment configuration for MongoDB Date Validator", "connection_string": "mongodb://localhost:27017", "databases": "dev_db,test_db", "exclude_collections": "system.users,system.roles,debug_logs", "batch_size": 500, "max_concurrent_collections": 5, "output_file": "dev_invalid_dates_report.csv", "sample_limit": 5, "log_level": "DEBUG", "no_summary": false, "save_summary": true}