# MongoDB Date Validator Configuration Guide

This guide explains how to configure the MongoDB Date Validator using JSON configuration files.

## Configuration File Structure

The validator uses JSON configuration files to define connection settings, scanning parameters, and output options. This approach provides better organization and reusability compared to command-line arguments.

### Basic Configuration (config.json)

```json
{
  "connection_string": "mongodb://localhost:27017",
  "databases": null,
  "exclude_collections": "system.users,system.roles,system.indexes",
  "batch_size": 1000,
  "max_concurrent_collections": 10,
  "output_file": "invalid_dates_report.csv",
  "sample_limit": 10,
  "log_level": "INFO",
  "no_summary": false,
  "save_summary": false
}
```

## Configuration Parameters

### Connection Settings

- **`connection_string`** (string, required): MongoDB connection URI
  - Examples:
    - Local: `"mongodb://localhost:27017"`
    - Authenticated: `"*******************************************"`
    - Atlas: `"mongodb+srv://user:<EMAIL>"`
    - Replica Set: `"mongodb://host1:27017,host2:27017/db?replicaSet=rs0"`

### Database Selection

- **`databases`** (string or null): Comma-separated list of databases to scan
  - `null`: Scan all non-system databases (recommended)
  - `"db1,db2,db3"`: Scan only specified databases

- **`exclude_collections`** (string or null): Collections to skip during scanning
  - Common exclusions: `"system.users,system.roles,system.indexes,logs,temp_data"`
  - `null`: Don't exclude any collections

### Performance Settings

- **`batch_size`** (integer): Documents processed per batch
  - Small databases: 500-1000
  - Large databases: 2000-5000
  - Default: 1000

- **`max_concurrent_collections`** (integer): Collections processed simultaneously
  - Conservative: 5-10
  - Aggressive: 15-25
  - Default: 10

### Output Configuration

- **`output_file`** (string): CSV report filename
  - Default: `"invalid_dates_report.csv"`
  - Include path if needed: `"reports/validation_results.csv"`

- **`sample_limit`** (integer): Max invalid records to collect per collection
  - Memory-conscious: 3-5
  - Detailed analysis: 15-20
  - Default: 10

### Logging and Reporting

- **`log_level`** (string): Logging verbosity
  - `"DEBUG"`: Detailed progress information
  - `"INFO"`: Standard progress updates (recommended)
  - `"WARNING"`: Only warnings and errors
  - `"ERROR"`: Only errors

- **`no_summary`** (boolean): Skip console summary output
  - `false`: Show summary (recommended)
  - `true`: Suppress summary (for automated scripts)

- **`save_summary`** (boolean): Generate JSON summary report
  - `false`: CSV only
  - `true`: Generate both CSV and JSON reports

## Sample Configurations

### Production Environment

```json
{
  "_comment": "Production settings with authentication and performance optimization",
  "connection_string": "*****************************************************************************",
  "databases": "production_db,analytics_db,user_data",
  "exclude_collections": "system.users,system.roles,logs,temp_collections,cache_data",
  "batch_size": 2000,
  "max_concurrent_collections": 15,
  "output_file": "production_date_validation.csv",
  "sample_limit": 20,
  "log_level": "WARNING",
  "no_summary": false,
  "save_summary": true
}
```

### Development Environment

```json
{
  "_comment": "Development settings with verbose logging",
  "connection_string": "mongodb://localhost:27017",
  "databases": "dev_db,test_db",
  "exclude_collections": "system.users,system.roles,debug_logs",
  "batch_size": 500,
  "max_concurrent_collections": 5,
  "output_file": "dev_validation_report.csv",
  "sample_limit": 5,
  "log_level": "DEBUG",
  "no_summary": false,
  "save_summary": true
}
```

### High Performance (Large Databases)

```json
{
  "_comment": "Optimized for scanning very large databases",
  "connection_string": "mongodb://localhost:27017",
  "databases": null,
  "exclude_collections": "system.users,system.roles,system.indexes,logs,temp_data,cache",
  "batch_size": 5000,
  "max_concurrent_collections": 25,
  "output_file": "large_db_validation.csv",
  "sample_limit": 3,
  "log_level": "ERROR",
  "no_summary": true,
  "save_summary": true
}
```

## Usage Patterns

### 1. Default Configuration
```bash
# Uses config.json in current directory
python mongo_date_validator.py
```

### 2. Custom Configuration File
```bash
# Use specific config file
python mongo_date_validator.py --config production_config.json
```

### 3. Config File with Overrides
```bash
# Use config file but override specific settings
python mongo_date_validator.py --databases "urgent_db" --log-level DEBUG
```

### 4. Environment-Specific Configs
```bash
# Development
python mongo_date_validator.py --config config_samples/development_config.json

# Production
python mongo_date_validator.py --config config_samples/production_config.json

# High Performance
python mongo_date_validator.py --config config_samples/high_performance_config.json
```

## Best Practices

1. **Start Conservative**: Begin with smaller batch sizes and fewer concurrent collections
2. **Monitor Resources**: Watch CPU and memory usage during large scans
3. **Use Exclusions**: Skip unnecessary collections to improve performance
4. **Save Summaries**: Enable JSON summaries for detailed analysis
5. **Environment-Specific**: Maintain separate configs for dev/staging/production
6. **Version Control**: Store configuration files in version control (excluding sensitive credentials)

## Security Considerations

- **Never commit credentials**: Use environment variables or secure credential management
- **Use read-only accounts**: The validator only needs read access to databases
- **Network security**: Ensure secure connections for remote MongoDB instances
- **Audit access**: Monitor who runs validations and when

## Troubleshooting Configuration

### Common Issues

1. **Connection failures**: Verify connection string format and credentials
2. **Permission errors**: Ensure MongoDB user has read access to target databases
3. **Memory issues**: Reduce batch_size and max_concurrent_collections
4. **Slow performance**: Increase batch_size and concurrent collections (within limits)

### Testing Configuration

Use the test script to verify your configuration:
```bash
python test_validator.py "your_connection_string"
```
